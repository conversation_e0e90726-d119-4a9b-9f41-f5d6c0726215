const Tips = () => {
  const articles = [
    {
      id: 1,
      title: "Rutinitas Perawatan Kulit yang Tepat",
      excerpt:
        "Pelajari langkah-langkah dasar perawatan kulit yang efektif untuk mendapatkan kulit sehat dan berseri.",
      image:
        "https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
      category: "Skincare Routine",
      readTime: "5 min read",
      date: "15 Des 2024",
    },
    {
      id: 2,
      title: "Ra<PERSON>ia Kulit Sehat ala Dermatologis",
      excerpt:
        "Tips dan trik dari para ahli dermatologi untuk menjaga kesehatan kulit dalam jangka panjang.",
      image:
        "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
      category: "Expert Tips",
      readTime: "7 min read",
      date: "12 Des 2024",
    },
    {
      id: 3,
      title: "Cara Memilih Sunscreen yang Tepat",
      excerpt:
        "Panduan lengkap memilih sunscreen sesuai jenis kulit dan aktivitas harian Anda.",
      image:
        "https://images.unsplash.com/photo-1571781926291-c477ebfd024b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
      category: "Product Guide",
      readTime: "4 min read",
      date: "10 Des 2024",
    },
    {
      id: 4,
      title: "Mengatasi Jerawat dengan Cara Alami",
      excerpt:
        "Solusi alami dan efektif untuk mengatasi masalah jerawat tanpa efek samping.",
      image:
        "https://images.unsplash.com/photo-1608248543803-ba4f8c70ae0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
      category: "Acne Treatment",
      readTime: "6 min read",
      date: "8 Des 2024",
    },
    {
      id: 5,
      title: "Anti-Aging: Mencegah Penuaan Dini",
      excerpt:
        "Strategi pencegahan penuaan dini yang bisa dimulai dari usia muda.",
      image:
        "https://images.unsplash.com/photo-1612817288484-6f916006741a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
      category: "Anti-Aging",
      readTime: "8 min read",
      date: "5 Des 2024",
    },
    {
      id: 6,
      title: "Skincare untuk Kulit Sensitif",
      excerpt:
        "Panduan khusus perawatan kulit untuk pemilik kulit sensitif dan mudah iritasi.",
      image:
        "https://images.unsplash.com/photo-1570194065650-d99fb4bedf0a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80",
      category: "Sensitive Skin",
      readTime: "5 min read",
      date: "3 Des 2024",
    },
  ];

  const categories = [
    "All",
    "Skincare Routine",
    "Expert Tips",
    "Product Guide",
    "Acne Treatment",
    "Anti-Aging",
    "Sensitive Skin",
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Tips & Artikel Skincare
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Dapatkan tips terbaru dan panduan lengkap untuk perawatan kulit
              yang optimal
            </p>
          </div>
        </div>
      </div>

      {/* Filter Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                className="px-6 py-2 rounded-full border border-gray-300 text-gray-700 hover:bg-orange-50 hover:border-orange-300 hover:text-orange-700 transition-colors duration-200"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Articles Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {articles.map((article) => (
            <article
              key={article.id}
              className="card group hover:shadow-xl transition-shadow duration-300 cursor-pointer"
            >
              <div className="aspect-w-16 aspect-h-12 mb-4">
                <img
                  src={article.image}
                  alt={article.title}
                  className="w-full h-48 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="inline-block px-3 py-1 text-xs font-medium text-orange-600 bg-orange-50 rounded-full">
                    {article.category}
                  </span>
                  <span className="text-xs text-gray-500">
                    {article.readTime}
                  </span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 group-hover:text-orange-600 transition-colors duration-200">
                  {article.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {article.excerpt}
                </p>
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <span className="text-sm text-gray-500">{article.date}</span>
                  <button className="text-orange-600 hover:text-orange-700 font-medium text-sm">
                    Baca Selengkapnya →
                  </button>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>

      {/* Newsletter Section */}
      <div className="bg-orange-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Dapatkan Tips Skincare Terbaru
          </h2>
          <p className="text-orange-100 mb-8 max-w-2xl mx-auto">
            Berlangganan newsletter kami untuk mendapatkan tips perawatan kulit,
            artikel terbaru, dan penawaran eksklusif langsung di inbox Anda.
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Masukkan email Anda"
              className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:outline-none"
            />
            <button className="bg-white text-orange-600 font-medium px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Tips;
