# Skincare Website Frontend

Website frontend untuk platform analisis kulit dengan teknologi AI yang memberikan rekomendasi perawatan kulit yang tepat.

## 🚀 Teknologi yang Digunakan

- **React.js** - Library JavaScript untuk membangun user interface
- **Vite** - Build tool yang cepat untuk development
- **Tailwind CSS** - Framework CSS utility-first
- **React Router** - Library untuk navigasi dan routing

## 📋 Fitur

- **Hero Section** - Landing page dengan upload foto untuk analisis kulit
- **Ana<PERSON>is Kulit** - Menampilkan hasil analisis dengan kategori kelembaban, kadar minyak, dan jenis kulit
- **Rekomendasi Produk** - Grid produk skincare yang direkomendasikan
- **Tips & Artikel** - Section artikel dan tips perawatan kulit
- **Halaman Produk** - Katalog lengkap produk skincare
- **<PERSON><PERSON> Tips** - Kumpulan artikel dan panduan perawatan kulit
- **<PERSON>aman Kontak** - Form kontak dan informasi customer service
- **Responsive Design** - Tam<PERSON>lan yang optimal di semua device

## 🛠️ Instalasi dan Menjalankan Project

1. **Clone repository**

   ```bash
   git clone <repository-url>
   cd frontend2
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Jalankan development server**

   ```bash
   npm run dev
   ```

4. **Buka browser dan akses**
   ```
   http://localhost:5173
   ```

## 📁 Struktur Project

```
src/
├── components/          # Komponen React yang dapat digunakan kembali
│   ├── Header.jsx      # Navigation header
│   ├── Hero.jsx        # Hero section dengan upload foto
│   ├── SkinAnalysis.jsx # Hasil analisis kulit
│   ├── ProductRecommendation.jsx # Rekomendasi produk
│   ├── TipsArticles.jsx # Tips dan artikel
│   └── Footer.jsx      # Footer dengan social media links
├── pages/              # Halaman-halaman utama
│   ├── Home.jsx        # Halaman beranda
│   ├── Products.jsx    # Halaman produk
│   ├── Tips.jsx        # Halaman tips & artikel
│   └── Contact.jsx     # Halaman kontak
├── App.jsx             # Komponen utama dengan routing
├── main.jsx            # Entry point aplikasi
└── index.css           # Styling dengan Tailwind CSS
```

## 🎨 Design System

### Warna

- **Primary**: Orange gradient (#ed7c4a - #de6330)
- **Secondary**: Blue gradient (#0ea5e9 - #0284c7)
- **Background**: White dan Gray tones

### Typography

- **Font**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

## 📱 Halaman yang Tersedia

1. **Beranda (/)** - Landing page dengan hero section, analisis kulit, rekomendasi produk, dan tips
2. **Produk (/produk)** - Katalog produk skincare dengan filter kategori
3. **Tips (/tips)** - Artikel dan tips perawatan kulit dengan newsletter signup
4. **Kontak (/kontak)** - Form kontak dan informasi customer service

## 🔧 Scripts yang Tersedia

- `npm run dev` - Menjalankan development server
- `npm run build` - Build untuk production
- `npm run preview` - Preview build production
- `npm run lint` - Menjalankan ESLint

## 📦 Dependencies

### Production

- react
- react-dom
- react-router-dom

### Development

- @vitejs/plugin-react
- vite
- tailwindcss
- postcss
- autoprefixer
- eslint

## 🌟 Fitur Utama

### Upload dan Analisis Foto

- Upload foto wajah untuk analisis kulit
- Tampilan hasil analisis dengan visualisasi yang menarik
- Rekomendasi perawatan berdasarkan hasil analisis

### Responsive Design

- Mobile-first approach
- Optimized untuk semua ukuran layar
- Touch-friendly interface

### Modern UI/UX

- Clean dan minimalist design
- Smooth animations dan transitions
- Intuitive navigation

## 🚀 Deployment

Project ini siap untuk di-deploy ke platform seperti:

- Vercel
- Netlify
- GitHub Pages
- Firebase Hosting

Jalankan `npm run build` untuk membuat production build.

## 📄 License

MIT License - lihat file LICENSE untuk detail lengkap.
