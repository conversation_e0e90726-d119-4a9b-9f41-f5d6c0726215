import { useRef, useState } from "react";

const SkinAnalysisDashboard = () => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [captureMode, setCaptureMode] = useState(false);
  const fileInputRef = useRef(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);

  // Debug: Test if component loads
  console.log("SkinAnalysisDashboard component loaded");

  // Handle file upload
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith("image/")) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
      setAnalysisResult(null);
    }
  };

  // Start camera
  const startCamera = async () => {
    try {
      setCaptureMode(true);
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "user" },
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error("Error accessing camera:", error);
      alert(
        "Tidak dapat mengakses kamera. Pastikan izin kamera telah diberikan."
      );
    }
  };

  // Capture photo from camera
  const capturePhoto = () => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext("2d");

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    canvas.toBlob((blob) => {
      const file = new File([blob], "captured-photo.jpg", {
        type: "image/jpeg",
      });
      setSelectedImage(file);
      setImagePreview(canvas.toDataURL());
      stopCamera();
    });
  };

  // Stop camera
  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach((track) => track.stop());
    }
    setCaptureMode(false);
  };

  // Simulate ML analysis (replace with actual ML model call)
  const analyzeImage = async () => {
    if (!selectedImage) return;

    setIsAnalyzing(true);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Mock analysis result (replace with actual ML model response)
    const mockResult = {
      skinType: "Kombinasi",
      oilLevel: 65,
      moistureLevel: 45,
      acneRisk: 30,
      wrinkleLevel: 15,
      poreSize: "Sedang",
      skinTone: "Medium",
      recommendations: [
        "Gunakan pembersih wajah yang lembut 2x sehari",
        "Aplikasikan pelembab ringan di area kering",
        "Gunakan sunscreen SPF 30+ setiap hari",
        "Hindari produk yang mengandung alkohol tinggi",
      ],
      detectedIssues: [
        { name: "Komedo", severity: "Ringan", area: "T-Zone" },
        { name: "Pori Besar", severity: "Sedang", area: "Hidung" },
        { name: "Kulit Kering", severity: "Ringan", area: "Pipi" },
      ],
    };

    setAnalysisResult(mockResult);
    setIsAnalyzing(false);
  };

  // Reset analysis
  const resetAnalysis = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setAnalysisResult(null);
    setCaptureMode(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Dashboard Analisis Kulit Wajah
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Upload foto atau ambil foto langsung untuk mendapatkan analisis
            kulit wajah yang akurat menggunakan teknologi AI
          </p>
          <div className="mt-4 p-4 bg-green-100 rounded-lg">
            <p className="text-green-800">✅ Dashboard berhasil dimuat!</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Image Upload/Capture */}
          <div className="space-y-6">
            {/* Upload/Capture Controls */}
            <div className="card">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Upload atau Ambil Foto
              </h2>

              {!captureMode ? (
                <div className="space-y-4">
                  {/* Upload Button */}
                  <div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full flex items-center justify-center px-6 py-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-400 hover:bg-orange-50 transition-colors duration-200"
                    >
                      <svg
                        className="w-8 h-8 text-gray-400 mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <span className="text-gray-600">
                        Klik untuk upload foto
                      </span>
                    </button>
                  </div>

                  {/* Camera Button */}
                  <div className="text-center">
                    <span className="text-gray-500">atau</span>
                  </div>
                  <button
                    onClick={startCamera}
                    className="w-full btn-secondary flex items-center justify-center"
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    Ambil Foto dengan Kamera
                  </button>
                </div>
              ) : (
                /* Camera View */
                <div className="space-y-4">
                  <div className="relative bg-black rounded-lg overflow-hidden">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute inset-0 border-2 border-white/30 rounded-lg pointer-events-none">
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 border-2 border-orange-400 rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex space-x-4">
                    <button
                      onClick={capturePhoto}
                      className="flex-1 btn-primary"
                    >
                      📸 Ambil Foto
                    </button>
                    <button
                      onClick={stopCamera}
                      className="px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
                    >
                      Batal
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Image Preview */}
            {imagePreview && (
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Preview Foto
                </h3>
                <div className="relative">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="w-full h-64 object-cover rounded-lg"
                  />
                  <button
                    onClick={resetAnalysis}
                    className="absolute top-2 right-2 p-2 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors duration-200"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                {!isAnalyzing && !analysisResult && (
                  <button
                    onClick={analyzeImage}
                    className="w-full mt-4 btn-primary"
                  >
                    🔍 Mulai Analisis
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Right Panel - Analysis Results */}
          <div className="space-y-6">
            {isAnalyzing && (
              <div className="card text-center">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Menganalisis Kulit Wajah...
                </h3>
                <p className="text-gray-600">
                  AI sedang memproses foto Anda. Mohon tunggu sebentar.
                </p>
              </div>
            )}

            {analysisResult && (
              <div className="space-y-6">
                {/* Overall Results */}
                <div className="card">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Hasil Analisis Kulit
                  </h3>

                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {analysisResult.moistureLevel}%
                      </div>
                      <div className="text-sm text-gray-600">Kelembapan</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        {analysisResult.oilLevel}%
                      </div>
                      <div className="text-sm text-gray-600">Kadar Minyak</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Jenis Kulit:</span>
                      <span className="font-medium text-purple-600">
                        {analysisResult.skinType}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Ukuran Pori:</span>
                      <span className="font-medium">
                        {analysisResult.poreSize}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Risiko Jerawat:</span>
                      <span className="font-medium text-orange-600">
                        {analysisResult.acneRisk}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tingkat Kerutan:</span>
                      <span className="font-medium text-green-600">
                        {analysisResult.wrinkleLevel}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Detected Issues */}
                <div className="card">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Masalah yang Terdeteksi
                  </h3>
                  <div className="space-y-3">
                    {analysisResult.detectedIssues.map((issue, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                      >
                        <div>
                          <div className="font-medium text-gray-900">
                            {issue.name}
                          </div>
                          <div className="text-sm text-gray-600">
                            Area: {issue.area}
                          </div>
                        </div>
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded-full ${
                            issue.severity === "Ringan"
                              ? "bg-green-100 text-green-800"
                              : issue.severity === "Sedang"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {issue.severity}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Recommendations */}
                <div className="card">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Rekomendasi Perawatan
                  </h3>
                  <ul className="space-y-2">
                    {analysisResult.recommendations.map((rec, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-500 mr-2 mt-1">✓</span>
                        <span className="text-gray-700">{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4">
                  <button
                    onClick={resetAnalysis}
                    className="flex-1 px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
                  >
                    Analisis Ulang
                  </button>
                  <button className="flex-1 btn-primary">Simpan Hasil</button>
                </div>
              </div>
            )}

            {!imagePreview && !isAnalyzing && !analysisResult && (
              <div className="card text-center py-12">
                <svg
                  className="w-16 h-16 text-gray-300 mx-auto mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Belum Ada Foto
                </h3>
                <p className="text-gray-600">
                  Upload atau ambil foto untuk memulai analisis kulit wajah
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Hidden canvas for photo capture */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default SkinAnalysisDashboard;
