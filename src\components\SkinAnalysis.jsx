const SkinAnalysis = () => {
  const analysisResults = [
    {
      id: 1,
      type: "Kelembaban",
      subtitle: "Kelembapan",
      icon: (
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-blue-600"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2c-1.1 0-2 .9-2 2 0 1.1 2 4 2 4s2-2.9 2-4c0-1.1-.9-2-2-2z" />
            <path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z" />
          </svg>
        </div>
      ),
      color: "blue",
    },
    {
      id: 2,
      type: "Sedang",
      subtitle: "<PERSON><PERSON>",
      icon: (
        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-yellow-600"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2c-1.1 0-2 .9-2 2 0 1.1 2 4 2 4s2-2.9 2-4c0-1.1-.9-2-2-2z" />
            <ellipse
              cx="12"
              cy="3.5"
              rx="0.8"
              ry="1.2"
              fill="white"
              opacity="0.4"
            />
            <path d="M8 10c-0.8 0-1.5 0.7-1.5 1.5 0 0.8 1.5 3 1.5 3s1.5-2.2 1.5-3c0-0.8-0.7-1.5-1.5-1.5z" />
            <path d="M16 10c-0.8 0-1.5 0.7-1.5 1.5 0 0.8 1.5 3 1.5 3s1.5-2.2 1.5-3c0-0.8-0.7-1.5-1.5-1.5z" />
            <path d="M12 16c-0.8 0-1.5 0.7-1.5 1.5 0 0.8 1.5 3 1.5 3s1.5-2.2 1.5-3c0-0.8-0.7-1.5-1.5-1.5z" />
          </svg>
        </div>
      ),
      color: "yellow",
    },
    {
      id: 3,
      type: "Kombinasi",
      subtitle: "Jenis Kulit",
      icon: (
        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-purple-600"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <circle cx="12" cy="12" r="10" />
            <circle cx="9" cy="9" r="1.5" fill="white" />
            <circle cx="15" cy="9" r="1.5" fill="white" />
            <path
              d="M8 15c0 2.21 1.79 4 4 4s4-1.79 4-4"
              stroke="white"
              strokeWidth="1.5"
              fill="none"
              strokeLinecap="round"
            />
            <path
              d="M12 2 A10 10 0 0 1 22 12 A10 10 0 0 1 12 22 A10 10 0 0 1 2 12 A10 10 0 0 1 12 2 Z"
              fill="rgba(147, 51, 234, 0.1)"
            />
          </svg>
        </div>
      ),
      color: "purple",
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="section-title">Hasil Analisa Kulit</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {analysisResults.map((result) => (
            <div key={result.id} className="text-center group">
              <div className="flex justify-center mb-4 transform group-hover:scale-110 transition-transform duration-200">
                {result.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {result.type}
              </h3>
              <p className="text-gray-600">{result.subtitle}</p>
            </div>
          ))}
        </div>

        {/* Analysis Details */}
        <div className="mt-16 bg-gray-50 rounded-2xl p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Detail Analisis
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Tingkat Kelembaban:</span>
                  <span className="font-medium text-blue-600">75%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kadar Minyak:</span>
                  <span className="font-medium text-blue-600">Sedang</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jenis Kulit:</span>
                  <span className="font-medium text-purple-600">Kombinasi</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kondisi Pori:</span>
                  <span className="font-medium text-green-600">Normal</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Rekomendasi Perawatan
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Gunakan pelembab ringan 2x sehari
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Aplikasikan sunscreen SPF 30+
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Eksfoliasi 1-2x seminggu
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Hindari produk berbahan alkohol
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkinAnalysis;
