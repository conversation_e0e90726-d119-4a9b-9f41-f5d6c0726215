const SkinAnalysis = () => {
  const analysisResults = [
    {
      id: 1,
      type: "Kelembaban",
      subtitle: "Kelembapan",
      icon: (
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-blue-600"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2.1c-1.1 0-2 .9-2 2 0 1.1.9 2 2 2s2-.9 2-2c0-1.1-.9-2-2-2zm0 18.8c-4.4 0-8-3.6-8-8 0-2.2.9-4.2 2.3-5.7l1.4 1.4c-1 1.1-1.7 2.6-1.7 4.3 0 3.3 2.7 6 6 6s6-2.7 6-6c0-1.7-.6-3.2-1.7-4.3l1.4-1.4c1.4 1.5 2.3 3.5 2.3 5.7 0 4.4-3.6 8-8 8z" />
          </svg>
        </div>
      ),
      color: "blue",
    },
    {
      id: 2,
      type: "Sedang",
      subtitle: "Kadar Minyak",
      icon: (
        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-yellow-600"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
            <circle cx="12" cy="9" r="1.5" fill="white" />
            <ellipse
              cx="10"
              cy="7"
              rx="1"
              ry="0.5"
              fill="white"
              opacity="0.7"
            />
            <ellipse
              cx="14"
              cy="8"
              rx="0.8"
              ry="0.4"
              fill="white"
              opacity="0.5"
            />
          </svg>
        </div>
      ),
      color: "yellow",
    },
    {
      id: 3,
      type: "Kombinasi",
      subtitle: "Jenis Kulit",
      icon: (
        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-purple-600"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" />
            <circle cx="8.5" cy="9.5" r="1.5" fill="white" />
            <circle cx="15.5" cy="9.5" r="1.5" fill="white" />
            <path
              d="M8 14c0 2.21 1.79 4 4 4s4-1.79 4-4"
              stroke="white"
              strokeWidth="1.5"
              fill="none"
              strokeLinecap="round"
            />
            <path
              d="M12 4c4.41 0 8 3.59 8 8 0 1.85-.63 3.55-1.69 4.9L12 12V4z"
              fill="rgba(147, 51, 234, 0.3)"
            />
            <path
              d="M4 12c0-1.85.63-3.55 1.69-4.9L12 12v8c-4.41 0-8-3.59-8-8z"
              fill="rgba(147, 51, 234, 0.6)"
            />
          </svg>
        </div>
      ),
      color: "purple",
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="section-title">Hasil Analisa Kulit</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {analysisResults.map((result) => (
            <div key={result.id} className="text-center group">
              <div className="flex justify-center mb-4 transform group-hover:scale-110 transition-transform duration-200">
                {result.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {result.type}
              </h3>
              <p className="text-gray-600">{result.subtitle}</p>
            </div>
          ))}
        </div>

        {/* Analysis Details */}
        <div className="mt-16 bg-gray-50 rounded-2xl p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Detail Analisis
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Tingkat Kelembaban:</span>
                  <span className="font-medium text-blue-600">75%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kadar Minyak:</span>
                  <span className="font-medium text-blue-600">Sedang</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jenis Kulit:</span>
                  <span className="font-medium text-purple-600">Kombinasi</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kondisi Pori:</span>
                  <span className="font-medium text-green-600">Normal</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Rekomendasi Perawatan
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Gunakan pelembab ringan 2x sehari
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Aplikasikan sunscreen SPF 30+
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Eksfoliasi 1-2x seminggu
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Hindari produk berbahan alkohol
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkinAnalysis;
