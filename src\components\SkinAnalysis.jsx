const SkinAnalysis = () => {
  const analysisResults = [
    {
      id: 1,
      type: "Kelembaban",
      subtitle: "Kelembapan",
      icon: (
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-blue-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M9 12h6"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M12 9v6"
            />
          </svg>
        </div>
      ),
      color: "blue",
    },
    {
      id: 2,
      type: "Sedang",
      subtitle: "<PERSON><PERSON>yak",
      icon: (
        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-yellow-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <circle cx="12" cy="12" r="10" strokeWidth={2} />
            <circle cx="12" cy="12" r="6" fill="currentColor" opacity="0.2" />
            <circle cx="10" cy="10" r="1.5" fill="currentColor" />
            <circle cx="14" cy="8" r="1" fill="currentColor" />
            <circle cx="15" cy="14" r="0.8" fill="currentColor" />
            <circle cx="8" cy="14" r="1.2" fill="currentColor" />
          </svg>
        </div>
      ),
      color: "yellow",
    },
    {
      id: 3,
      type: "Kombinasi",
      subtitle: "Jenis Kulit",
      icon: (
        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-purple-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <circle cx="12" cy="12" r="10" strokeWidth={2} />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 2v20"
            />
            <path
              d="M12 2 A10 10 0 0 1 22 12 Z"
              fill="currentColor"
              opacity="0.3"
            />
            <path
              d="M12 12 A10 10 0 0 1 2 12 A10 10 0 0 1 12 2 Z"
              fill="currentColor"
              opacity="0.1"
            />
          </svg>
        </div>
      ),
      color: "purple",
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="section-title">Hasil Analisa Kulit</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {analysisResults.map((result) => (
            <div key={result.id} className="text-center group">
              <div className="flex justify-center mb-4 transform group-hover:scale-110 transition-transform duration-200">
                {result.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {result.type}
              </h3>
              <p className="text-gray-600">{result.subtitle}</p>
            </div>
          ))}
        </div>

        {/* Analysis Details */}
        <div className="mt-16 bg-gray-50 rounded-2xl p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Detail Analisis
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Tingkat Kelembaban:</span>
                  <span className="font-medium text-blue-600">75%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kadar Minyak:</span>
                  <span className="font-medium text-blue-600">Sedang</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jenis Kulit:</span>
                  <span className="font-medium text-purple-600">Kombinasi</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kondisi Pori:</span>
                  <span className="font-medium text-green-600">Normal</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Rekomendasi Perawatan
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Gunakan pelembab ringan 2x sehari
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Aplikasikan sunscreen SPF 30+
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Eksfoliasi 1-2x seminggu
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  Hindari produk berbahan alkohol
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkinAnalysis;
